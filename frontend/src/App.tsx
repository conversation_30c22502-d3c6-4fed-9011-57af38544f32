import React from 'react'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { LoginForm } from './components/login-form'
import { Button } from './components/ui/button'
import './App.css'

function Dashboard() {
  const { authState, logout } = useAuth()

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold text-gray-900">
              ShipTag Dashboard
            </h1>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                Welcome, {authState.user?.email}
              </span>
              <Button variant="outline" onClick={logout}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            🎉 Authentication Successful!
          </h2>
          <p className="text-gray-600 mb-6">
            You have successfully logged in with OTP authentication.
          </p>
          <div className="bg-white rounded-lg shadow p-6 max-w-md mx-auto">
            <h3 className="font-semibold mb-2">User Information</h3>
            <div className="text-left space-y-2">
              <p><strong>Email:</strong> {authState.user?.email}</p>
              <p><strong>Name:</strong> {authState.user?.full_name || 'Not set'}</p>
              <p><strong>ID:</strong> {authState.user?.id}</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

function AuthenticatedApp() {
  const { authState } = useAuth()

  if (authState.isAuthenticated) {
    return <Dashboard />
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <LoginForm />
      </div>
    </div>
  )
}

function App() {
  return (
    <AuthProvider>
      <AuthenticatedApp />
    </AuthProvider>
  )
}

export default App
