import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  full_name?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: AuthUser;
}

export interface SessionResponse {
  success: boolean;
  session: {
    sessionId: string;
    expiresAt: string;
    user: AuthUser & {
      user_type: string;
      is_verified: boolean;
      email_verified: boolean;
      created_at: string;
    };
  };
}

export interface SendOTPResponse {
  success: boolean;
  message: string;
  expiresAt?: string;
}

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080',
    timeout: 10000,
    withCredentials: true, // Include cookies in requests
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      // Get token from localStorage
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error: AxiosError) => {
      // Handle common error scenarios
      if (error.response?.status === 401) {
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('auth_token');
        // Dispatch custom event for auth context to handle
        window.dispatchEvent(new CustomEvent('auth:logout'));
      }

      // Handle network errors
      if (!error.response) {
        console.error('Network error:', error.message);
        return Promise.reject({
          error: 'Network error',
          message: 'Unable to connect to the server. Please check your internet connection.',
        });
      }

      // Return structured error response
      const errorResponse = {
        error: error.response.data?.error || 'Request failed',
        message: error.response.data?.message || 'An unexpected error occurred',
        status: error.response.status,
      };

      return Promise.reject(errorResponse);
    }
  );

  return client;
};

// Create the API client instance
export const apiClient = createApiClient();

// Auth API methods
export const authApi = {
  /**
   * Send OTP to email
   */
  sendOTP: async (email: string): Promise<SendOTPResponse> => {
    const response = await apiClient.post<SendOTPResponse>('/api/auth/send-otp', { email });
    return response.data;
  },

  /**
   * Verify OTP and get auth token
   */
  verifyOTP: async (email: string, otp: string): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/api/auth/verify-otp', { email, otp });
    
    // Store token in localStorage if verification successful
    if (response.data.success && response.data.token) {
      localStorage.setItem('auth_token', response.data.token);
    }
    
    return response.data;
  },

  /**
   * Resend OTP
   */
  resendOTP: async (email: string): Promise<SendOTPResponse> => {
    const response = await apiClient.post<SendOTPResponse>('/api/auth/resend-otp', { email });
    return response.data;
  },

  /**
   * Get current session
   */
  getSession: async (): Promise<SessionResponse> => {
    const response = await apiClient.get<SessionResponse>('/api/auth/session');
    return response.data;
  },

  /**
   * Sign out
   */
  signOut: async (): Promise<ApiResponse> => {
    const response = await apiClient.post<ApiResponse>('/api/auth/signout');
    
    // Clear token from localStorage
    localStorage.removeItem('auth_token');
    
    return response.data;
  },

  /**
   * Health check
   */
  healthCheck: async (): Promise<ApiResponse> => {
    const response = await apiClient.get<ApiResponse>('/api/auth/health');
    return response.data;
  },
};

// Utility functions
export const setAuthToken = (token: string): void => {
  localStorage.setItem('auth_token', token);
};

export const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token');
};

export const clearAuthToken = (): void => {
  localStorage.removeItem('auth_token');
};

export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.error) {
    return error.error;
  }
  
  return 'An unexpected error occurred';
};

export default apiClient;
