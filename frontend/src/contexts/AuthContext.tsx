import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { authApi, AuthUser, handleApiError, clearAuthToken, isAuthenticated } from '@/lib/api';

// Types
export interface AuthState {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface OTPState {
  email: string;
  isLoading: boolean;
  error: string | null;
  expiresAt: Date | null;
  canResend: boolean;
  resendCooldown: number;
}

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: AuthUser }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' };

type OTPAction =
  | { type: 'OTP_SEND_START'; payload: string }
  | { type: 'OTP_SEND_SUCCESS'; payload: { expiresAt: Date } }
  | { type: 'OTP_SEND_ERROR'; payload: string }
  | { type: 'OTP_VERIFY_START' }
  | { type: 'OTP_VERIFY_SUCCESS' }
  | { type: 'OTP_VERIFY_ERROR'; payload: string }
  | { type: 'OTP_RESEND_COOLDOWN'; payload: number }
  | { type: 'OTP_CLEAR_ERROR' }
  | { type: 'OTP_RESET' };

// Initial states
const initialAuthState: AuthState = {
  user: null,
  isLoading: false,
  isAuthenticated: false,
  error: null,
};

const initialOTPState: OTPState = {
  email: '',
  isLoading: false,
  error: null,
  expiresAt: null,
  canResend: true,
  resendCooldown: 0,
};

// Reducers
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return { ...state, isLoading: true, error: null };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload,
        error: null,
      };
    case 'AUTH_ERROR':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: action.payload,
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: null,
      };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const otpReducer = (state: OTPState, action: OTPAction): OTPState => {
  switch (action.type) {
    case 'OTP_SEND_START':
      return {
        ...state,
        email: action.payload,
        isLoading: true,
        error: null,
      };
    case 'OTP_SEND_SUCCESS':
      return {
        ...state,
        isLoading: false,
        expiresAt: action.payload.expiresAt,
        canResend: false,
        resendCooldown: 60, // 60 seconds cooldown
        error: null,
      };
    case 'OTP_SEND_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case 'OTP_VERIFY_START':
      return { ...state, isLoading: true, error: null };
    case 'OTP_VERIFY_SUCCESS':
      return { ...state, isLoading: false, error: null };
    case 'OTP_VERIFY_ERROR':
      return { ...state, isLoading: false, error: action.payload };
    case 'OTP_RESEND_COOLDOWN':
      return {
        ...state,
        resendCooldown: action.payload,
        canResend: action.payload === 0,
      };
    case 'OTP_CLEAR_ERROR':
      return { ...state, error: null };
    case 'OTP_RESET':
      return initialOTPState;
    default:
      return state;
  }
};

// Context
interface AuthContextType {
  // Auth state
  authState: AuthState;
  otpState: OTPState;
  
  // Auth actions
  checkAuth: () => Promise<void>;
  logout: () => Promise<void>;
  clearAuthError: () => void;
  
  // OTP actions
  sendOTP: (email: string) => Promise<boolean>;
  verifyOTP: (otp: string) => Promise<boolean>;
  resendOTP: () => Promise<boolean>;
  clearOTPError: () => void;
  resetOTP: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, authDispatch] = useReducer(authReducer, initialAuthState);
  const [otpState, otpDispatch] = useReducer(otpReducer, initialOTPState);

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, []);

  // Listen for logout events from API interceptor
  useEffect(() => {
    const handleLogout = () => {
      authDispatch({ type: 'AUTH_LOGOUT' });
      otpDispatch({ type: 'OTP_RESET' });
    };

    window.addEventListener('auth:logout', handleLogout);
    return () => window.removeEventListener('auth:logout', handleLogout);
  }, []);

  // Resend cooldown timer
  useEffect(() => {
    if (otpState.resendCooldown > 0) {
      const timer = setTimeout(() => {
        otpDispatch({ type: 'OTP_RESEND_COOLDOWN', payload: otpState.resendCooldown - 1 });
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [otpState.resendCooldown]);

  // Auth methods
  const checkAuth = async (): Promise<void> => {
    if (!isAuthenticated()) {
      return;
    }

    authDispatch({ type: 'AUTH_START' });
    
    try {
      const response = await authApi.getSession();
      authDispatch({ type: 'AUTH_SUCCESS', payload: response.session.user });
    } catch (error) {
      const errorMessage = handleApiError(error);
      authDispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      clearAuthToken();
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await authApi.signOut();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      authDispatch({ type: 'AUTH_LOGOUT' });
      otpDispatch({ type: 'OTP_RESET' });
      clearAuthToken();
    }
  };

  const clearAuthError = (): void => {
    authDispatch({ type: 'CLEAR_ERROR' });
  };

  // OTP methods
  const sendOTP = async (email: string): Promise<boolean> => {
    otpDispatch({ type: 'OTP_SEND_START', payload: email });
    
    try {
      const response = await authApi.sendOTP(email);
      const expiresAt = response.expiresAt ? new Date(response.expiresAt) : new Date(Date.now() + 10 * 60 * 1000);
      otpDispatch({ type: 'OTP_SEND_SUCCESS', payload: { expiresAt } });
      return true;
    } catch (error) {
      const errorMessage = handleApiError(error);
      otpDispatch({ type: 'OTP_SEND_ERROR', payload: errorMessage });
      return false;
    }
  };

  const verifyOTP = async (otp: string): Promise<boolean> => {
    otpDispatch({ type: 'OTP_VERIFY_START' });
    authDispatch({ type: 'AUTH_START' });
    
    try {
      const response = await authApi.verifyOTP(otpState.email, otp);
      
      if (response.success && response.user) {
        otpDispatch({ type: 'OTP_VERIFY_SUCCESS' });
        authDispatch({ type: 'AUTH_SUCCESS', payload: response.user });
        return true;
      } else {
        throw new Error(response.message || 'Verification failed');
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      otpDispatch({ type: 'OTP_VERIFY_ERROR', payload: errorMessage });
      authDispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      return false;
    }
  };

  const resendOTP = async (): Promise<boolean> => {
    if (!otpState.canResend || !otpState.email) {
      return false;
    }
    
    return sendOTP(otpState.email);
  };

  const clearOTPError = (): void => {
    otpDispatch({ type: 'OTP_CLEAR_ERROR' });
  };

  const resetOTP = (): void => {
    otpDispatch({ type: 'OTP_RESET' });
  };

  const value: AuthContextType = {
    authState,
    otpState,
    checkAuth,
    logout,
    clearAuthError,
    sendOTP,
    verifyOTP,
    resendOTP,
    clearOTPError,
    resetOTP,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
