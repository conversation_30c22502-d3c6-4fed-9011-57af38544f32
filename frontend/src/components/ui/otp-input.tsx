import React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react';
import { cn } from '@/lib/utils';

interface OTPInputProps {
  length?: number;
  value: string;
  onChange: (value: string) => void;
  onComplete?: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function OTPInput({
  length = 6,
  value,
  onChange,
  onComplete,
  disabled = false,
  error = false,
  className,
}: OTPInputProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Focus management
  useEffect(() => {
    if (inputRefs.current[activeIndex]) {
      inputRefs.current[activeIndex]?.focus();
    }
  }, [activeIndex]);

  // Handle completion
  useEffect(() => {
    if (value.length === length && onComplete) {
      onComplete(value);
    }
  }, [value, length, onComplete]);

  const handleChange = (index: number, digit: string) => {
    if (disabled) return;

    // Only allow single digits
    if (digit.length > 1) {
      digit = digit.slice(-1);
    }

    // Only allow numbers
    if (digit && !/^\d$/.test(digit)) {
      return;
    }

    const newValue = value.split('');
    newValue[index] = digit;
    const updatedValue = newValue.join('').slice(0, length);
    
    onChange(updatedValue);

    // Move to next input if digit was entered
    if (digit && index < length - 1) {
      setActiveIndex(index + 1);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    switch (e.key) {
      case 'Backspace':
        e.preventDefault();
        if (value[index]) {
          // Clear current input
          handleChange(index, '');
        } else if (index > 0) {
          // Move to previous input and clear it
          setActiveIndex(index - 1);
          handleChange(index - 1, '');
        }
        break;
      
      case 'Delete':
        e.preventDefault();
        handleChange(index, '');
        break;
      
      case 'ArrowLeft':
        e.preventDefault();
        if (index > 0) {
          setActiveIndex(index - 1);
        }
        break;
      
      case 'ArrowRight':
        e.preventDefault();
        if (index < length - 1) {
          setActiveIndex(index + 1);
        }
        break;
      
      case 'Home':
        e.preventDefault();
        setActiveIndex(0);
        break;
      
      case 'End':
        e.preventDefault();
        setActiveIndex(length - 1);
        break;
    }
  };

  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    if (disabled) return;
    
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain');
    const digits = pastedData.replace(/\D/g, '').slice(0, length);
    
    if (digits) {
      onChange(digits);
      // Focus the last filled input or the next empty one
      const nextIndex = Math.min(digits.length, length - 1);
      setActiveIndex(nextIndex);
    }
  };

  const handleClick = (index: number) => {
    setActiveIndex(index);
  };

  return (
    <div className={cn('flex gap-2 justify-center', className)}>
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={value[index] || ''}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onFocus={() => handleFocus(index)}
          onPaste={handlePaste}
          onClick={() => handleClick(index)}
          disabled={disabled}
          className={cn(
            'w-12 h-12 text-center text-lg font-semibold',
            'border-2 rounded-lg',
            'transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-offset-2',
            // Default state
            'border-gray-300 bg-white text-gray-900',
            'focus:border-blue-500 focus:ring-blue-500',
            // Error state
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            // Disabled state
            disabled && 'bg-gray-100 text-gray-500 cursor-not-allowed',
            // Active state
            activeIndex === index && !disabled && 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20',
            // Filled state
            value[index] && 'border-green-500 bg-green-50'
          )}
          aria-label={`Digit ${index + 1} of ${length}`}
          autoComplete="one-time-code"
        />
      ))}
    </div>
  );
}

export default OTPInput;
