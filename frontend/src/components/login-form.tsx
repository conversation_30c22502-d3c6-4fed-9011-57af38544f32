import React, { useState, useEffect } from "react"
import { GalleryVerticalEnd, ArrowLeft, Mail, Clock, RefreshCw } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { OTPInput } from "@/components/ui/otp-input"
import { useAuth } from "@/contexts/AuthContext"

type LoginStep = 'email' | 'otp'

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [step, setStep] = useState<LoginStep>('email')
  const [email, setEmail] = useState('')
  const [otp, setOTP] = useState('')
  const [timeLeft, setTimeLeft] = useState(0)

  const {
    authState,
    otpState,
    sendOTP,
    verifyOTP,
    resendOTP,
    clearOTPError,
    clearAuthError
  } = useAuth()

  // Timer for OTP expiration
  useEffect(() => {
    if (otpState.expiresAt && step === 'otp') {
      const updateTimer = () => {
        const now = new Date().getTime()
        const expiry = new Date(otpState.expiresAt!).getTime()
        const remaining = Math.max(0, Math.floor((expiry - now) / 1000))
        setTimeLeft(remaining)

        if (remaining === 0) {
          // OTP expired, go back to email step
          setStep('email')
        }
      }

      updateTimer()
      const interval = setInterval(updateTimer, 1000)
      return () => clearInterval(interval)
    }
  }, [otpState.expiresAt, step])

  // Clear errors when switching steps
  useEffect(() => {
    clearOTPError()
    clearAuthError()
  }, [step])

  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) return

    const success = await sendOTP(email.trim())
    if (success) {
      setStep('otp')
      setOTP('')
    }
  }

  const handleVerifyOTP = async (otpValue: string) => {
    if (otpValue.length === 6) {
      const success = await verifyOTP(otpValue)
      if (!success) {
        setOTP('') // Clear OTP on error
      }
    }
  }

  const handleResendOTP = async () => {
    const success = await resendOTP()
    if (success) {
      setOTP('')
    }
  }

  const handleBackToEmail = () => {
    setStep('email')
    setOTP('')
    clearOTPError()
    clearAuthError()
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const currentError = otpState.error || authState.error

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col items-center gap-2">
          <a
            href="#"
            className="flex flex-col items-center gap-2 font-medium"
          >
            <div className="flex size-8 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-6" />
            </div>
            <span className="sr-only">ShipTag</span>
          </a>
          <h1 className="text-xl font-bold">Welcome to ShipTag</h1>
          <div className="text-center text-sm text-muted-foreground">
            {step === 'email' ? (
              <>
                Enter your email to receive a login code
              </>
            ) : (
              <>
                We sent a 6-digit code to{" "}
                <span className="font-medium text-foreground">{email}</span>
              </>
            )}
          </div>
        </div>

        {/* Email Step */}
        {step === 'email' && (
          <form onSubmit={handleSendOTP}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={otpState.isLoading}
                />
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={otpState.isLoading || !email.trim()}
              >
                {otpState.isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Sending Code...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Send Login Code
                  </>
                )}
              </Button>
            </div>
          </form>
        )}

        {/* OTP Step */}
        {step === 'otp' && (
          <div className="flex flex-col gap-6">
            <div className="grid gap-3">
              <Label className="text-center">Enter verification code</Label>
              <OTPInput
                value={otp}
                onChange={setOTP}
                onComplete={handleVerifyOTP}
                disabled={otpState.isLoading || authState.isLoading}
                error={!!currentError}
                className="justify-center"
              />

              {timeLeft > 0 && (
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  Code expires in {formatTime(timeLeft)}
                </div>
              )}
            </div>

            <div className="flex flex-col gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleBackToEmail}
                disabled={otpState.isLoading || authState.isLoading}
                className="w-full"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Email
              </Button>

              <Button
                type="button"
                variant="ghost"
                onClick={handleResendOTP}
                disabled={!otpState.canResend || otpState.isLoading}
                className="w-full"
              >
                {otpState.resendCooldown > 0 ? (
                  `Resend code in ${otpState.resendCooldown}s`
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Resend Code
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Error Display */}
        {currentError && (
          <div className="rounded-md bg-red-50 border border-red-200 p-3">
            <p className="text-sm text-red-800">{currentError}</p>
          </div>
        )}

        {/* Loading State */}
        {(otpState.isLoading || authState.isLoading) && (
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <RefreshCw className="h-4 w-4 animate-spin" />
            {step === 'email' ? 'Sending code...' : 'Verifying code...'}
          </div>
        )}
      </div>

      <div className="text-muted-foreground text-center text-xs text-balance">
        By continuing, you agree to our{" "}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          Terms of Service
        </a>{" "}
        and{" "}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          Privacy Policy
        </a>
        .
      </div>
    </div>
  )
}
