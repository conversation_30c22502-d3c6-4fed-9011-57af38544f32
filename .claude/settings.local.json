{"permissions": {"allow": ["Bash(npm run:*)", "Bash(find:*)", "Bash(npm view:*)", "Bash(npx prisma generate:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npm start)", "WebFetch(domain:authjs.dev)", "WebFetch(domain:github.com)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(mkdir:*)", "Bash(node:*)", "Bash(npm install:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(test:*)", "<PERSON><PERSON>(timeout 5 npm run dev)", "Bash(-b cookies.txt -w \"\\nHTTP Status: %{http_code}\\n\")", "Bash(rm:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "<PERSON><PERSON>(cat:*)"], "deny": []}}