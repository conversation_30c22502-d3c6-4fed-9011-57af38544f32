import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

/**
 * Rate limiter for OTP requests
 * Max 3 requests per 15 minutes per email
 */
export const otpRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each email to 3 requests per windowMs
  message: {
    error: 'Too many OTP requests',
    message: 'Too many OTP requests from this email. Please try again in 15 minutes.',
    retryAfter: 15 * 60, // seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    // Use email from request body as the key
    return req.body.email || req.ip;
  },
  handler: (req: Request, res: Response) => {
    res.status(429).json({
      error: 'Too many OTP requests',
      message: 'Too many OTP requests from this email. Please try again in 15 minutes.',
      retryAfter: 15 * 60,
    });
  },
});

/**
 * Rate limiter for OTP verification attempts
 * Max 5 attempts per 5 minutes per IP
 */
export const verifyOtpRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many verification attempts',
    message: 'Too many OTP verification attempts. Please try again in 5 minutes.',
    retryAfter: 5 * 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    res.status(429).json({
      error: 'Too many verification attempts',
      message: 'Too many OTP verification attempts. Please try again in 5 minutes.',
      retryAfter: 5 * 60,
    });
  },
});

/**
 * General API rate limiter
 * Max 100 requests per 15 minutes per IP
 */
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests',
    message: 'Too many requests from this IP. Please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * In-memory store for tracking OTP attempts per email
 */
class OTPAttemptTracker {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private readonly maxAttempts = 3;
  private readonly windowMs = 15 * 60 * 1000; // 15 minutes

  /**
   * Check if email has exceeded OTP attempts
   */
  isBlocked(email: string): boolean {
    const record = this.attempts.get(email);
    if (!record) return false;

    // Reset if window has passed
    if (Date.now() > record.resetTime) {
      this.attempts.delete(email);
      return false;
    }

    return record.count >= this.maxAttempts;
  }

  /**
   * Record an OTP attempt
   */
  recordAttempt(email: string): void {
    const now = Date.now();
    const record = this.attempts.get(email);

    if (!record || now > record.resetTime) {
      // First attempt or window reset
      this.attempts.set(email, {
        count: 1,
        resetTime: now + this.windowMs,
      });
    } else {
      // Increment existing count
      record.count++;
    }
  }

  /**
   * Get remaining time until reset (in seconds)
   */
  getResetTime(email: string): number {
    const record = this.attempts.get(email);
    if (!record) return 0;

    const remaining = Math.max(0, record.resetTime - Date.now());
    return Math.ceil(remaining / 1000);
  }

  /**
   * Clear attempts for email (used after successful verification)
   */
  clearAttempts(email: string): void {
    this.attempts.delete(email);
  }
}

export const otpAttemptTracker = new OTPAttemptTracker();
