import jwt from 'jsonwebtoken';

export interface JWTPayload {
  userId: string;
  email: string;
  sessionId: string;
}

/**
 * Generate JWT token
 */
export function generateJWT(payload: JWTPayload): string {
  const secret = process.env.AUTH_SECRET;
  if (!secret) {
    throw new Error('AUTH_SECRET environment variable is required');
  }

  return jwt.sign(payload, secret, {
    expiresIn: '24h',
    issuer: 'shiptag-auth',
    audience: 'shiptag-app',
  });
}

/**
 * Verify JWT token
 */
export function verifyJWT(token: string): JWTPayload {
  const secret = process.env.AUTH_SECRET;
  if (!secret) {
    throw new Error('AUTH_SECRET environment variable is required');
  }

  try {
    const decoded = jwt.verify(token, secret, {
      issuer: 'shiptag-auth',
      audience: 'shiptag-app',
    }) as JWTPayload;

    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid token');
    } else {
      throw new Error('Token verification failed');
    }
  }
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}
