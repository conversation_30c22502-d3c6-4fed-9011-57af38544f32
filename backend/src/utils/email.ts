import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export interface SendOTPEmailParams {
  email: string;
  otp: string;
  expiresInMinutes: number;
}

/**
 * Send OTP email using Resend
 */
export async function sendOTPEmail({ email, otp, expiresInMinutes }: SendOTPEmailParams): Promise<void> {
  try {
    const { data, error } = await resend.emails.send({
      from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
      to: [email],
      subject: 'Your Login Code',
      html: generateOTPEmailHTML(otp, expiresInMinutes),
    });

    if (error) {
      console.error('Failed to send OTP email:', error);
      throw new Error('Failed to send OTP email');
    }

    console.log('OTP email sent successfully:', data?.id);
  } catch (error) {
    console.error('Error sending OTP email:', error);
    throw new Error('Failed to send OTP email');
  }
}

/**
 * Generate HTML template for OTP email
 */
function generateOTPEmailHTML(otp: string, expiresInMinutes: number): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Login Code</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .container {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 30px;
          text-align: center;
        }
        .otp-code {
          font-size: 32px;
          font-weight: bold;
          letter-spacing: 8px;
          color: #2563eb;
          background: #eff6ff;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border: 2px dashed #2563eb;
        }
        .warning {
          background: #fef3c7;
          border: 1px solid #f59e0b;
          border-radius: 6px;
          padding: 15px;
          margin: 20px 0;
          color: #92400e;
        }
        .footer {
          margin-top: 30px;
          font-size: 14px;
          color: #6b7280;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Your Login Code</h1>
        <p>Use this code to complete your login:</p>
        
        <div class="otp-code">${otp}</div>
        
        <div class="warning">
          <strong>⚠️ Important:</strong> This code expires in ${expiresInMinutes} minutes and can only be used once.
        </div>
        
        <p>If you didn't request this code, please ignore this email.</p>
        
        <div class="footer">
          <p>This is an automated message, please do not reply.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
