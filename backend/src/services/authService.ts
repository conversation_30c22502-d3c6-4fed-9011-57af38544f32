import crypto from 'crypto';
import { PrismaClient } from '../generated/prisma';
import { generateOTP, hashOTP, verifyOTP, getOTPExpiration, generateSessionToken, getSessionExpiration } from '../utils/otp';
import { sendOTPEmail, isValidEmail } from '../utils/email';
import { generateJWT } from '../utils/jwt';
import { otpAttemptTracker } from '../utils/rateLimiter';

const prisma = new PrismaClient();

export interface SendOTPResult {
  success: boolean;
  message: string;
  expiresAt?: Date;
}

export interface VerifyOTPResult {
  success: boolean;
  message: string;
  token?: string;
  user?: {
    id: string;
    email: string;
    full_name?: string;
  };
}

/**
 * Send OTP to email address
 */
export async function sendOTP(email: string): Promise<SendOTPResult> {
  try {
    // Validate email format
    if (!isValidEmail(email)) {
      return {
        success: false,
        message: 'Invalid email format',
      };
    }

    // Check rate limiting
    if (otpAttemptTracker.isBlocked(email)) {
      const resetTime = otpAttemptTracker.getResetTime(email);
      return {
        success: false,
        message: `Too many OTP requests. Please try again in ${Math.ceil(resetTime / 60)} minutes.`,
      };
    }

    // Generate OTP
    const otp = generateOTP();
    const hashedOTP = await hashOTP(otp);
    const expiresAt = getOTPExpiration(parseInt(process.env.OTP_EXPIRES_IN_MINUTES || '10'));

    // Clean up any existing OTP for this email
    await prisma.otp_token.deleteMany({
      where: { email },
    });

    // Store OTP in database
    await prisma.otp_token.create({
      data: {
        email,
        code: hashedOTP,
        expires_at: expiresAt,
        attempts: 0,
        verified: false,
      },
    });

    // Send OTP email
    await sendOTPEmail({
      email,
      otp,
      expiresInMinutes: parseInt(process.env.OTP_EXPIRES_IN_MINUTES || '10'),
    });

    // Record attempt for rate limiting
    otpAttemptTracker.recordAttempt(email);

    return {
      success: true,
      message: 'OTP sent successfully',
      expiresAt,
    };
  } catch (error) {
    console.error('Error sending OTP:', error);
    return {
      success: false,
      message: 'Failed to send OTP. Please try again.',
    };
  }
}

/**
 * Verify OTP and create session
 */
export async function verifyOTPAndCreateSession(email: string, otp: string): Promise<VerifyOTPResult> {
  try {
    // Validate inputs
    if (!isValidEmail(email)) {
      return {
        success: false,
        message: 'Invalid email format',
      };
    }

    if (!otp || otp.length !== 6 || !/^\d{6}$/.test(otp)) {
      return {
        success: false,
        message: 'Invalid OTP format. Please enter a 6-digit code.',
      };
    }

    // Find OTP record
    const otpRecord = await prisma.otp_token.findFirst({
      where: {
        email,
        verified: false,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    if (!otpRecord) {
      return {
        success: false,
        message: 'No valid OTP found. Please request a new code.',
      };
    }

    // Check if OTP is expired
    if (otpRecord.expires_at < new Date()) {
      await prisma.otp_token.delete({
        where: { id: otpRecord.id },
      });
      return {
        success: false,
        message: 'OTP has expired. Please request a new code.',
      };
    }

    // Check attempt limit
    const maxAttempts = parseInt(process.env.OTP_MAX_ATTEMPTS || '3');
    if (otpRecord.attempts && otpRecord.attempts >= maxAttempts) {
      await prisma.otp_token.delete({
        where: { id: otpRecord.id },
      });
      return {
        success: false,
        message: 'Too many failed attempts. Please request a new code.',
      };
    }

    // Verify OTP
    const isValidOTP = await verifyOTP(otp, otpRecord.code);
    
    if (!isValidOTP) {
      // Increment attempts
      await prisma.otp_token.update({
        where: { id: otpRecord.id },
        data: {
          attempts: (otpRecord.attempts || 0) + 1,
        },
      });

      return {
        success: false,
        message: 'Invalid OTP. Please check your code and try again.',
      };
    }

    // Mark OTP as verified
    await prisma.otp_token.update({
      where: { id: otpRecord.id },
      data: { verified: true },
    });

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // Create new user
      user = await prisma.user.create({
        data: {
          auth_user_id: crypto.randomUUID(), // Generate proper UUID
          email,
          user_type: 'PERSONAL',
          is_verified: true,
          email_verified: true,
          last_signin_at: new Date(),
        },
      });
    } else {
      // Update last signin
      await prisma.user.update({
        where: { id: user.id },
        data: {
          last_signin_at: new Date(),
          is_verified: true,
          email_verified: true,
        },
      });
    }

    // Create session
    const sessionToken = generateSessionToken();
    const sessionExpires = getSessionExpiration();

    const session = await prisma.auth_session.create({
      data: {
        user_id: user.id,
        token: sessionToken,
        expires_at: sessionExpires,
      },
    });

    // Generate JWT
    const jwtToken = generateJWT({
      userId: user.id,
      email: user.email,
      sessionId: session.id,
    });

    // Clean up OTP record
    await prisma.otp_token.delete({
      where: { id: otpRecord.id },
    });

    // Clear rate limiting for this email
    otpAttemptTracker.clearAttempts(email);

    return {
      success: true,
      message: 'Login successful',
      token: jwtToken,
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name || undefined,
      },
    };
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return {
      success: false,
      message: 'Failed to verify OTP. Please try again.',
    };
  }
}

/**
 * Get current session info
 */
export async function getCurrentSession(sessionId: string) {
  try {
    const session = await prisma.auth_session.findUnique({
      where: { id: sessionId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            full_name: true,
            user_type: true,
            is_verified: true,
            email_verified: true,
            created_at: true,
          },
        },
      },
    });

    if (!session || session.expires_at < new Date()) {
      return null;
    }

    return {
      sessionId: session.id,
      expiresAt: session.expires_at,
      user: session.user,
    };
  } catch (error) {
    console.error('Error getting current session:', error);
    return null;
  }
}

/**
 * Sign out user and invalidate session
 */
export async function signOut(sessionId: string): Promise<boolean> {
  try {
    await prisma.auth_session.delete({
      where: { id: sessionId },
    });
    return true;
  } catch (error) {
    console.error('Error signing out:', error);
    return false;
  }
}
