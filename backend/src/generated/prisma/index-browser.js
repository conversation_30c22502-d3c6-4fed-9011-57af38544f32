
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  auth_user_id: 'auth_user_id',
  organization_id: 'organization_id',
  user_type: 'user_type',
  full_name: 'full_name',
  email: 'email',
  phone: 'phone',
  role: 'role',
  invited_by: 'invited_by',
  is_invitation_accepted: 'is_invitation_accepted',
  is_verified: 'is_verified',
  created_at: 'created_at',
  updated_at: 'updated_at',
  email_verified: 'email_verified',
  last_signin_at: 'last_signin_at'
};

exports.Prisma.Auth_sessionScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  token: 'token',
  expires_at: 'expires_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Otp_tokenScalarFieldEnum = {
  id: 'id',
  email: 'email',
  phone: 'phone',
  code: 'code',
  attempts: 'attempts',
  expires_at: 'expires_at',
  verified: 'verified',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.User_profileScalarFieldEnum = {
  user_id: 'user_id',
  government_id_number: 'government_id_number',
  government_id_image: 'government_id_image',
  certificate_number: 'certificate_number',
  document_image: 'document_image',
  cr_number: 'cr_number',
  unified_national_number: 'unified_national_number',
  cr_document: 'cr_document',
  vat_id: 'vat_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Bank_detailScalarFieldEnum = {
  id: 'id',
  profile_type: 'profile_type',
  user_id: 'user_id',
  bank_name: 'bank_name',
  account_name: 'account_name',
  iban: 'iban',
  swift_code: 'swift_code',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at',
  plan_id: 'plan_id'
};

exports.Prisma.User_invitationScalarFieldEnum = {
  id: 'id',
  organization_id: 'organization_id',
  email: 'email',
  phone: 'phone',
  delivery_method: 'delivery_method',
  invited_by: 'invited_by',
  role: 'role',
  token: 'token',
  is_accepted: 'is_accepted',
  expires_at: 'expires_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CountryScalarFieldEnum = {
  id: 'id',
  code: 'code',
  code_iso3: 'code_iso3',
  name: 'name',
  phone_prefix: 'phone_prefix',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CityScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  name_ar: 'name_ar',
  country_code: 'country_code',
  lat: 'lat',
  long: 'long',
  region: 'region',
  type: 'type',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  title: 'title',
  name: 'name',
  phone: 'phone',
  address_1: 'address_1',
  address_2: 'address_2',
  city_name: 'city_name',
  state: 'state',
  zip: 'zip',
  country: 'country',
  city_id: 'city_id',
  lat: 'lat',
  longitude: 'longitude',
  is_default: 'is_default',
  type: 'type',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Einvoice_settingScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  allowed_carrier_codes: 'allowed_carrier_codes',
  allowed_payments: 'allowed_payments',
  default_address_id: 'default_address_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CarrierScalarFieldEnum = {
  id: 'id',
  carrier_code: 'carrier_code',
  logo_url: 'logo_url',
  display_order: 'display_order',
  max_weight: 'max_weight',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.BranchScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  station: 'station',
  url: 'url',
  lat: 'lat',
  long: 'long',
  open_time: 'open_time',
  close_time: 'close_time',
  contact_name: 'contact_name',
  contact_number: 'contact_number',
  is_active: 'is_active',
  type: 'type',
  additional_info: 'additional_info',
  created_at: 'created_at',
  updated_at: 'updated_at',
  city_code: 'city_code',
  carrier_code: 'carrier_code'
};

exports.Prisma.Carrier_city_serviceScalarFieldEnum = {
  id: 'id',
  pickup_type: 'pickup_type',
  dropoff_type: 'dropoff_type',
  notes: 'notes',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at',
  city_code: 'city_code',
  carrier_code: 'carrier_code'
};

exports.Prisma.Carrier_serviceScalarFieldEnum = {
  id: 'id',
  carrier_code: 'carrier_code',
  name: 'name',
  pickup_type: 'pickup_type',
  dropoff_type: 'dropoff_type',
  region: 'region',
  pickup_time: 'pickup_time',
  transit_time: 'transit_time',
  delivery_time: 'delivery_time',
  delivery_days: 'delivery_days',
  pickup_days: 'pickup_days',
  dimensional_limit: 'dimensional_limit',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ZoneScalarFieldEnum = {
  id: 'id',
  country_code: 'country_code',
  carrier_code: 'carrier_code',
  zone_code: 'zone_code',
  zone_name: 'zone_name',
  description: 'description',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Zone_to_cityScalarFieldEnum = {
  zone_id: 'zone_id',
  city_id: 'city_id'
};

exports.Prisma.CostScalarFieldEnum = {
  id: 'id',
  from_country_code: 'from_country_code',
  from_zone: 'from_zone',
  to_country_code: 'to_country_code',
  to_zone: 'to_zone',
  carrier_code: 'carrier_code',
  from_weight: 'from_weight',
  to_weight: 'to_weight',
  rate_type: 'rate_type',
  cost_amount: 'cost_amount',
  base_fee: 'base_fee',
  currency: 'currency',
  valid_from: 'valid_from',
  valid_to: 'valid_to',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.RateScalarFieldEnum = {
  id: 'id',
  from_country_code: 'from_country_code',
  from_zone: 'from_zone',
  to_country_code: 'to_country_code',
  to_zone: 'to_zone',
  carrier_code: 'carrier_code',
  from_weight: 'from_weight',
  to_weight: 'to_weight',
  rate_type: 'rate_type',
  rate_amount: 'rate_amount',
  base_fee: 'base_fee',
  currency: 'currency',
  plan_id: 'plan_id',
  delivery_days: 'delivery_days',
  valid_from: 'valid_from',
  valid_to: 'valid_to',
  created_at: 'created_at',
  updated_at: 'updated_at',
  user_id: 'user_id',
  organization_id: 'organization_id'
};

exports.Prisma.PlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  description: 'description',
  price: 'price',
  duration_in_months: 'duration_in_months',
  is_active: 'is_active',
  upgrade_to: 'upgrade_to',
  trial_period_days: 'trial_period_days',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  description: 'description',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Plan_serviceScalarFieldEnum = {
  id: 'id',
  plan_id: 'plan_id',
  service_id: 'service_id',
  enabled: 'enabled',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.User_service_overrideScalarFieldEnum = {
  user_id: 'user_id',
  service_id: 'service_id',
  is_enabled: 'is_enabled',
  reason: 'reason',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CouponScalarFieldEnum = {
  id: 'id',
  code: 'code',
  discount_type: 'discount_type',
  discount_value: 'discount_value',
  usage_limit: 'usage_limit',
  used_count: 'used_count',
  max_usage_per_user: 'max_usage_per_user',
  min_order_value: 'min_order_value',
  plan_id: 'plan_id',
  expires_at: 'expires_at',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Coupon_usageScalarFieldEnum = {
  id: 'id',
  coupon_id: 'coupon_id',
  user_id: 'user_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.WalletScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  name: 'name',
  balance: 'balance',
  currency: 'currency',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  debit_type: 'debit_type',
  debit_id: 'debit_id',
  credit_type: 'credit_type',
  credit_id: 'credit_id',
  payment_gateway: 'payment_gateway',
  gateway_transaction_id: 'gateway_transaction_id',
  gateway_response_code: 'gateway_response_code',
  description: 'description',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Wallet_transactionScalarFieldEnum = {
  id: 'id',
  wallet_id: 'wallet_id',
  transaction_id: 'transaction_id',
  type: 'type',
  amount: 'amount',
  balance_before: 'balance_before',
  balance_after: 'balance_after',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  external_id: 'external_id',
  platform_id: 'platform_id',
  name: 'name',
  sku: 'sku',
  barcode: 'barcode',
  description: 'description',
  price: 'price',
  currency: 'currency',
  hs_code: 'hs_code',
  country_of_origin: 'country_of_origin',
  weight_grams: 'weight_grams',
  length_cm: 'length_cm',
  width_cm: 'width_cm',
  height_cm: 'height_cm',
  is_active: 'is_active',
  synced_at: 'synced_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Product_variantScalarFieldEnum = {
  id: 'id',
  product_id: 'product_id',
  user_id: 'user_id',
  name: 'name',
  sku: 'sku',
  barcode: 'barcode',
  price: 'price',
  weight_grams: 'weight_grams',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Product_imageScalarFieldEnum = {
  id: 'id',
  product_id: 'product_id',
  user_id: 'user_id',
  image_url: 'image_url',
  is_primary: 'is_primary',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.WarehouseScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  name: 'name',
  code: 'code',
  address_1: 'address_1',
  address_2: 'address_2',
  city_name: 'city_name',
  state: 'state',
  zip: 'zip',
  country: 'country',
  city_id: 'city_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  is_active: 'is_active'
};

exports.Prisma.Warehouse_binScalarFieldEnum = {
  id: 'id',
  warehouse_id: 'warehouse_id',
  user_id: 'user_id',
  code: 'code',
  zone: 'zone',
  description: 'description',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Product_inventoryScalarFieldEnum = {
  id: 'id',
  product_id: 'product_id',
  variant_id: 'variant_id',
  warehouse_id: 'warehouse_id',
  bin_id: 'bin_id',
  user_id: 'user_id',
  batch_number: 'batch_number',
  expiration_date: 'expiration_date',
  quantity: 'quantity',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  order_number: 'order_number',
  user_id: 'user_id',
  type: 'type',
  from_address: 'from_address',
  to_address: 'to_address',
  platform_order_id: 'platform_order_id',
  status: 'status',
  description: 'description',
  payment_status: 'payment_status',
  tracking_number: 'tracking_number',
  declared_value: 'declared_value',
  cod_amount: 'cod_amount',
  source: 'source',
  carrier_code: 'carrier_code',
  metadata: 'metadata',
  recipient_phone: 'recipient_phone',
  link_token: 'link_token',
  link_expires_at: 'link_expires_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Order_itemScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  order_id: 'order_id',
  product_id: 'product_id',
  product_name: 'product_name',
  product_sku: 'product_sku',
  quantity: 'quantity',
  unit_price: 'unit_price',
  total_price: 'total_price',
  metadata: 'metadata',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Order_chargeScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  order_id: 'order_id',
  name: 'name',
  type: 'type',
  amount: 'amount',
  currency: 'currency',
  metadata: 'metadata',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ParcelScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  order_id: 'order_id',
  length: 'length',
  width: 'width',
  height: 'height',
  actual_weight: 'actual_weight',
  dimensional_weight: 'dimensional_weight',
  final_weight: 'final_weight',
  tracking_number: 'tracking_number',
  metadata: 'metadata',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Tracking_dataScalarFieldEnum = {
  id: 'id',
  order_id: 'order_id',
  status: 'status',
  checkpoint: 'checkpoint',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PlatformScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  logo_url: 'logo_url',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Platform_integrationScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  platform_id: 'platform_id',
  external_shop_id: 'external_shop_id',
  access_token: 'access_token',
  refresh_token: 'refresh_token',
  token_expires_at: 'token_expires_at',
  shop_name: 'shop_name',
  connected_at: 'connected_at',
  is_active: 'is_active',
  updated_at: 'updated_at'
};

exports.Prisma.Platform_orderScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  platform_id: 'platform_id',
  external_order_id: 'external_order_id',
  status: 'status',
  customer_name: 'customer_name',
  customer_address_id: 'customer_address_id',
  items: 'items',
  raw_payload: 'raw_payload',
  synced_at: 'synced_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Product_sync_logScalarFieldEnum = {
  id: 'id',
  product_id: 'product_id',
  platform_id: 'platform_id',
  user_id: 'user_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  sync_status: 'sync_status',
  error_message: 'error_message'
};

exports.Prisma.Activity_logScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  table_name: 'table_name',
  record_id: 'record_id',
  action: 'action',
  message: 'message',
  level: 'level',
  data: 'data',
  created_at: 'created_at'
};

exports.Prisma.Audit_trailScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  table_name: 'table_name',
  record_id: 'record_id',
  field_changed: 'field_changed',
  old_value: 'old_value',
  new_value: 'new_value',
  created_at: 'created_at'
};

exports.Prisma.Webhook_logScalarFieldEnum = {
  id: 'id',
  payload: 'payload',
  source: 'source',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Content_translationScalarFieldEnum = {
  id: 'id',
  table_name: 'table_name',
  record_id: 'record_id',
  field_name: 'field_name',
  language_code: 'language_code',
  translated_value: 'translated_value',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserType = exports.$Enums.UserType = {
  PERSONAL: 'PERSONAL',
  FREELANCER: 'FREELANCER',
  COMPANY: 'COMPANY'
};

exports.CityType = exports.$Enums.CityType = {
  CITY: 'CITY',
  TOWN: 'TOWN',
  VILLAGE: 'VILLAGE',
  STATE: 'STATE',
  PROVINCE: 'PROVINCE',
  ROAD: 'ROAD',
  PEAK: 'PEAK',
  HAMLET: 'HAMLET',
  WATER: 'WATER',
  AMENITY: 'AMENITY',
  RIVER: 'RIVER',
  MAN_MADE: 'MAN_MADE',
  LOCALITY: 'LOCALITY',
  SHOP: 'SHOP',
  AERIALWAY: 'AERIALWAY',
  SUBURB: 'SUBURB',
  ISLAND: 'ISLAND'
};

exports.AddressType = exports.$Enums.AddressType = {
  SENDER: 'SENDER',
  RECEIVER: 'RECEIVER',
  BILLING: 'BILLING',
  COMPANY: 'COMPANY'
};

exports.BranchType = exports.$Enums.BranchType = {
  BRANCH: 'BRANCH',
  LOCKER: 'LOCKER',
  DROPOFF_POINT: 'DROPOFF_POINT',
  STATION: 'STATION',
  WSC: 'WSC',
  HO: 'HO',
  KKIA_GATEWAY: 'KKIA_GATEWAY',
  KFIA_GATEWAY: 'KFIA_GATEWAY',
  KF_CAUSEWAY: 'KF_CAUSEWAY',
  CLEARANCE: 'CLEARANCE',
  OFFICE: 'OFFICE',
  SHOP_AND_SHIP_OUTLET: 'SHOP_AND_SHIP_OUTLET',
  COLLECTION_POINT: 'COLLECTION_POINT',
  DROP_OFF: 'DROP_OFF',
  OUTLET: 'OUTLET'
};

exports.ServiceAvailabilityType = exports.$Enums.ServiceAvailabilityType = {
  BRANCH: 'BRANCH',
  LOCKER: 'LOCKER',
  DOOR: 'DOOR'
};

exports.OrderType = exports.$Enums.OrderType = {
  SHIPMENT: 'SHIPMENT',
  E_INVOICE_SHIPMENT: 'E_INVOICE_SHIPMENT',
  E_INVOICE_RETURN: 'E_INVOICE_RETURN',
  E_INVOICE_SALE: 'E_INVOICE_SALE'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  WAITING_FOR_PICKUP: 'WAITING_FOR_PICKUP',
  IN_TRANSIT: 'IN_TRANSIT',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PAID: 'PAID',
  UNPAID: 'UNPAID'
};

exports.Prisma.ModelName = {
  user: 'user',
  auth_session: 'auth_session',
  otp_token: 'otp_token',
  user_profile: 'user_profile',
  bank_detail: 'bank_detail',
  organization: 'organization',
  user_invitation: 'user_invitation',
  country: 'country',
  city: 'city',
  address: 'address',
  einvoice_setting: 'einvoice_setting',
  carrier: 'carrier',
  branch: 'branch',
  carrier_city_service: 'carrier_city_service',
  carrier_service: 'carrier_service',
  zone: 'zone',
  zone_to_city: 'zone_to_city',
  cost: 'cost',
  rate: 'rate',
  plan: 'plan',
  service: 'service',
  plan_service: 'plan_service',
  user_service_override: 'user_service_override',
  coupon: 'coupon',
  coupon_usage: 'coupon_usage',
  wallet: 'wallet',
  transaction: 'transaction',
  wallet_transaction: 'wallet_transaction',
  product: 'product',
  product_variant: 'product_variant',
  product_image: 'product_image',
  warehouse: 'warehouse',
  warehouse_bin: 'warehouse_bin',
  product_inventory: 'product_inventory',
  order: 'order',
  order_item: 'order_item',
  order_charge: 'order_charge',
  parcel: 'parcel',
  tracking_data: 'tracking_data',
  platform: 'platform',
  platform_integration: 'platform_integration',
  platform_order: 'platform_order',
  product_sync_log: 'product_sync_log',
  activity_log: 'activity_log',
  audit_trail: 'audit_trail',
  webhook_log: 'webhook_log',
  content_translation: 'content_translation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
