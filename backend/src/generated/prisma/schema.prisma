// ========================================================================
// --- Final Consolidated Prisma Schema
// --- Last Updated: June 30, 2025
// --- Organized by functional domains with descriptive comments
// ========================================================================

generator client {
  provider      = "prisma-client-js"
  output        = "../src/generated/prisma"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================================================
// --- ENUMS ---
// ========================================================================

// User account types for different business models
enum UserType {
  PERSONAL
  FREELANCER
  COMPANY
}

// User roles for different permissions
enum UserRole {
  ADMIN
  MEMBER
}

// Order types for different purposes
enum OrderType {
  SHIPMENT
  E_INVOICE_SHIPMENT
  E_INVOICE_RETURN
  E_INVOICE_SALE
}

// Physical location types for carrier branches
enum BranchType {
  BRANCH
  LOCKER
  DROPOFF_POINT
  STATION
  WSC
  HO
  KKIA_GATEWAY
  KFIA_GATEWAY
  KF_CAUSEWAY
  CLEARANCE
  OFFICE
  SHOP_AND_SHIP_OUTLET
  COLLECTION_POINT
  DROP_OFF
  OUTLET
}

// Payment status for orders
enum PaymentStatus {
  PAID
  UNPAID
}

// City types for statistics purposes
enum CityType {
  CITY
  TOWN
  VILLAGE
  STATE
  PROVINCE
  ROAD
  PEAK
  HAMLET
  WATER
  AMENITY
  RIVER
  MAN_MADE
  LOCALITY
  SHOP
  AERIALWAY
  SUBURB
  ISLAND
}

// Order status for orders
enum OrderStatus {
  PENDING
  WAITING_FOR_PICKUP
  IN_TRANSIT
  DELIVERED
  CANCELLED
}

// Service availability options for carrier services in cities
enum ServiceAvailabilityType {
  BRANCH
  LOCKER
  DOOR
}

// Address types for different purposes
enum AddressType {
  SENDER
  RECEIVER
  BILLING
  COMPANY
}

// ========================================================================
// --- USER & AUTHENTICATION MODELS ---
// ========================================================================

// Core user model representing all types of users in the system
model user {
  id                     String    @id @default(uuid()) @db.Uuid
  auth_user_id           String    @unique @db.Uuid
  organization_id        String?   @db.Uuid
  user_type              UserType
  full_name              String?
  email                  String    @unique
  phone                  String?   @unique
  role                   String    @default("member")
  invited_by             String?   @db.Uuid
  is_invitation_accepted Boolean?  @default(false)
  is_verified            Boolean?  @default(false)
  created_at             DateTime? @default(now()) @db.Timestamptz(6)
  updated_at             DateTime? @updatedAt @db.Timestamptz(6)
  email_verified         Boolean?  @default(false)
  last_signin_at         DateTime? @db.Timestamptz(6)

  // --- Relationships ---
  addresses              address[]
  audit_trails           audit_trail[]
  auth_sessions          auth_session[]
  bank_details           bank_detail[]
  warehouse_bins         warehouse_bin[]
  coupon_usages          coupon_usage[]
  activity_logs          activity_log[]
  order_charges          order_charge[]
  order_items            order_item[]
  orders                 order[]
  parcels                parcel[]
  platform_integrations  platform_integration[]
  platform_orders        platform_order[]
  product_images         product_image[]
  product_inventories    product_inventory[]
  product_sync_logs      product_sync_log[]
  product_variants       product_variant[]
  products               product[]
  rates                  rate[]
  transactions           transaction[]
  user_invitations       user_invitation[]
  user_service_overrides user_service_override[]
  invited_by_user        user?                   @relation("user_to_user", fields: [invited_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  invited_users          user[]                  @relation("user_to_user")
  organization           organization?           @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  wallet                 wallet?
  warehouses             warehouse[]
  einvoice_setting       einvoice_setting?

  // The new consolidated profile relationship
  profile user_profile?

  @@index([phone])
  @@index([auth_user_id], map: "idx_users_auth_user_id")
  @@index([organization_id], map: "idx_users_organization_id")
  @@map("user")
}

// Authentication session tokens for user sessions
model auth_session {
  id         String    @id @default(uuid()) @db.Uuid
  user_id    String    @db.Uuid
  token      String    @unique
  expires_at DateTime  @db.Timestamptz(6)
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user user @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([expires_at], map: "idx_auth_sessions_expires_at")
  @@index([token], map: "idx_auth_sessions_token")
  @@index([user_id], map: "idx_auth_sessions_user_id")
  @@map("auth_session")
}

// One-time password tokens for authentication
model otp_token {
  id         String    @id @default(uuid()) @db.Uuid
  email      String
  phone      String?
  code       String
  attempts   Int?      @default(0)
  expires_at DateTime  @db.Timestamptz(6)
  verified   Boolean?  @default(false)
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  @@index([code], map: "idx_otp_tokens_code")
  @@index([email], map: "idx_otp_tokens_email")
  @@index([expires_at], map: "idx_otp_tokens_expires_at")
  @@map("otp_token")
}

// ========================================================================
// --- USER PROFILE MODELS ---
// ========================================================================

// Consolidated profile for all user types (Personal, Freelancer, Company)
// The specific fields to be used are determined by the 'user_type' in the parent 'user' model.
model user_profile {
  user_id String @id @db.Uuid

  // --- Personal & Freelancer Fields ---
  // National ID or government-issued ID number.
  government_id_number String?
  // Image of the national or government ID.
  government_id_image  String?

  // --- Freelancer-Specific Fields ---
  // Freelancer certificate or license number.
  certificate_number String?
  // Image of the freelancer document/certificate.
  document_image     String?

  // --- Company-Specific Fields ---
  // Commercial Registration (CR) number for the company.
  cr_number               String?
  // Company's unique 10-digit national identifier.
  unified_national_number String?
  // Image of the Commercial Registration (CR) document.
  cr_document             String?
  // VAT identification number for the company.
  vat_id                  String?

  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  // Establishes a one-to-one relationship back to the user.
  // onDelete: Cascade ensures the profile is deleted if the user is deleted.
  user user @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("user_profile")
}

// Bank account details for user payouts and payments
model bank_detail {
  id           String    @id @default(uuid()) @db.Uuid
  profile_type String?
  user_id      String    @db.Uuid
  bank_name    String?
  account_name String?
  iban         String?
  swift_code   String?
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user user @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("bank_detail")
}

// ========================================================================
// --- ORGANIZATION & INVITATION MODELS ---
// ========================================================================

// Organizations that group multiple users together
model organization {
  id         String    @id @default(uuid()) @db.Uuid
  name       String
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  plan_id    String?   @db.Uuid

  // --- Relationships ---
  plan             plan?             @relation(fields: [plan_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  rates            rate[]
  user_invitations user_invitation[]
  users            user[]

  @@map("organization")
}

// Invitations to join organizations
model user_invitation {
  id              String    @id @default(uuid()) @db.Uuid
  organization_id String    @db.Uuid
  email           String?
  phone           String?
  delivery_method String?   @default("email")
  invited_by      String    @db.Uuid
  role            String    @default("member")
  token           String    @unique
  is_accepted     Boolean?  @default(false)
  expires_at      DateTime  @db.Timestamptz(6)
  created_at      DateTime? @default(now()) @db.Timestamptz(6)
  updated_at      DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user         user         @relation(fields: [invited_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization organization @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([organization_id], map: "idx_user_invitations_organization_id")
  @@index([token], map: "idx_user_invitations_token")
  @@map("user_invitation")
}

// ========================================================================
// --- LOCATION & ADDRESS MODELS ---
// ========================================================================

// Countries supported by the platform
model country {
  id           String    @id @default(uuid()) @db.Uuid
  code         String    @unique @db.Char(2)
  code_iso3    String    @unique @db.Char(3)
  name         String
  phone_prefix String
  is_active    Boolean?  @default(true)
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  cities     city[]
  costs_from cost[] @relation("cost_from_country")
  costs_to   cost[] @relation("cost_to_country")
  rates_from rate[] @relation("rate_from_country")
  rates_to   rate[] @relation("rate_to_country")
  zones      zone[]

  @@map("country")
}

// Cities within countries for shipping and addressing
model city {
  id           String    @id @default(uuid()) @db.Uuid
  code         String    @unique
  name         String
  name_ar      String?
  country_code String    @db.Char(2)
  lat          Decimal?  @db.Decimal(11, 8)
  long         Decimal?  @db.Decimal(12, 8)
  region       String?
  type         CityType?
  is_active    Boolean   @default(true)
  created_at   DateTime  @default(now()) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  country               country                @relation(fields: [country_code], references: [code])
  branches              branch[]
  carrier_city_services carrier_city_service[]
  zone_cities           zone_to_city[]
  warehouses            warehouse[]
  addresses             address[]

  @@map("city")
}

// Unified address model for all address types (sender, receiver, billing, company)
model address {
  id         String      @id @default(uuid()) @db.Uuid
  user_id    String?     @db.Uuid
  title      String?
  name       String?
  phone      String?
  address_1  String?
  address_2  String?
  city_name  String?
  state      String?
  zip        String?
  country    String?
  city_id    String?     @db.Uuid
  lat        Decimal?    @db.Decimal(11, 8)
  longitude  Decimal?    @db.Decimal(12, 8)
  is_default Boolean?    @default(false)
  type       AddressType
  created_at DateTime    @default(now()) @db.Timestamptz(6)
  updated_at DateTime    @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user              user?              @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  city              city?              @relation(fields: [city_id], references: [id])
  platform_orders   platform_order[]
  einvoice_settings einvoice_setting[]

  @@index([type], map: "idx_addresses_type")
  @@index([user_id], map: "idx_addresses_user_id")
  @@map("address")
}

// =======================================
// --- SETTINGS --- 
// =======================================

// e-invoice settings 
model einvoice_setting {
  id      String @id @default(uuid()) @db.Uuid
  user_id String @unique @db.Uuid
  user    user   @relation(fields: [user_id], references: [id])

  allowed_carrier_codes String[] // references carrier.carrier_code   // e.g. ["ARAMEX", "DHL"]
  allowed_payments      String[] // e.g. ["SHIPTAG_PAY", "COD"]

  default_address_id String?  @db.Uuid
  default_address    address? @relation(fields: [default_address_id], references: [id])

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}

// ========================================================================
// --- CARRIER & SHIPPING MODELS ---
// ========================================================================

// Shipping carriers (e.g., DHL, FedEx, local carriers)
model carrier {
  id            String   @id @default(uuid()) @db.Uuid
  carrier_code  String   @unique
  logo_url      String?
  display_order Int?     @default(0)
  max_weight    Decimal? @db.Decimal(12, 3)
  created_at    DateTime @default(now()) @db.Timestamptz(6)
  updated_at    DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  branches              branch[]
  carrier_city_services carrier_city_service[]
  zones                 zone[]
  carrier_services      carrier_service[]
  costs                 cost[]
  orders                order[]
  rates                 rate[]

  @@map("carrier")
}

// Physical locations where carriers operate (branches, lockers, drop-off points)
model branch {
  id              String     @id @default(uuid()) @db.Uuid
  name            String
  address         String
  station         String?
  url             String?
  lat             Decimal?   @db.Decimal(11, 8)
  long            Decimal?   @db.Decimal(12, 8)
  open_time       String?
  close_time      String?
  contact_name    String?
  contact_number  String?
  is_active       Boolean    @default(true)
  type            BranchType @default(BRANCH)
  additional_info Json?
  created_at      DateTime   @default(now()) @db.Timestamptz(6)
  updated_at      DateTime   @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  city_code    String
  carrier_code String
  city         city    @relation(fields: [city_code], references: [code])
  carrier      carrier @relation(fields: [carrier_code], references: [carrier_code])

  @@index([city_code], map: "idx_branches_city_code")
  @@index([carrier_code], map: "idx_branches_carrier_code")
  @@map("branch")
}

// Service availability for carriers in specific cities
model carrier_city_service {
  id           String                    @id @default(uuid()) @db.Uuid
  pickup_type  ServiceAvailabilityType[] @default([])
  dropoff_type ServiceAvailabilityType[] @default([])
  notes        String?                   @db.Text
  is_active    Boolean                   @default(true)
  created_at   DateTime                  @default(now()) @db.Timestamptz(6)
  updated_at   DateTime                  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  city_code    String // This is now required
  carrier_code String
  city         city    @relation(fields: [city_code], references: [code])
  carrier      carrier @relation(fields: [carrier_code], references: [carrier_code])

  @@unique([city_code, carrier_code])
  @@index([city_code], map: "idx_carrier_city_service_city_code")
  @@index([carrier_code], map: "idx_carrier_city_service_carrier_code")
  @@map("carrier_city_service")
}

// Service types offered by carriers (express, standard, etc.)
model carrier_service {
  id                String                    @id @default(uuid()) @db.Uuid // Unique ID
  carrier_code      String // FK to carrier.carrier_code
  name              String // Service name (e.g., Express)
  pickup_type       ServiceAvailabilityType[] @default([]) // Pickup types for this service
  dropoff_type      ServiceAvailabilityType[] @default([]) // Dropoff types for this service
  region            String? // Region served (optional)
  pickup_time       String? // Pickup time info (e.g., 2-4 days)
  transit_time      String? // Transit time info (e.g., 2-4 days)
  delivery_time     String? // Delivery time info (e.g., 2-4 days)
  delivery_days     String[]                  @default(["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]) // Optional, defaults to all days
  pickup_days       String[]                  @default(["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]) // Optional, defaults to all days
  dimensional_limit Json? // Dimensional limits in JSON
  created_at        DateTime                  @default(now()) @db.Timestamptz(6) // Creation timestamp
  updated_at        DateTime                  @updatedAt @db.Timestamptz(6) // Update timestamp

  // --- Relationships ---
  carrier carrier @relation(fields: [carrier_code], references: [carrier_code], onDelete: NoAction, onUpdate: NoAction)

  @@index([carrier_code], map: "idx_carrier_services_carrier_code")
  @@map("carrier_service")
}

// PURPOSE:
// carrier_city_service — defines what services are available for the carrier in a *specific city* (pickup/dropoff types at city level)
// carrier_service — defines the *types of services* (e.g., Express, Branch-to-Branch) offered globally or across cities by a carrier
// Both are needed because a service (e.g., Express) might be globally offered but may vary city to city in terms of availability (e.g., no pickup in CityX)

// ========================================================================
// --- ZONE & PRICING MODELS ---
// ========================================================================

// Shipping zones for carriers within countries
model zone {
  id           String    @id @default(uuid()) @db.Uuid
  country_code String    @db.Char(2)
  carrier_code String
  zone_code    String
  zone_name    String
  description  String?
  is_active    Boolean   @default(true)
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_at   DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  country     country        @relation(fields: [country_code], references: [code], onDelete: NoAction, onUpdate: NoAction)
  carrier     carrier        @relation(fields: [carrier_code], references: [carrier_code])
  zone_cities zone_to_city[]
  costs_from  cost[]         @relation("cost_from_zone")
  costs_to    cost[]         @relation("cost_to_zone")
  rates_from  rate[]         @relation("rate_from_zone")
  rates_to    rate[]         @relation("rate_to_zone")

  @@unique([country_code, carrier_code, zone_code])
  @@index([country_code], map: "idx_zones_country_code")
  @@map("zone")
}

// Mapping between zones and cities
model zone_to_city {
  zone_id String @db.Uuid
  city_id String @db.Uuid

  // --- Relationships ---
  zone zone @relation(fields: [zone_id], references: [id], onDelete: Cascade)
  city city @relation(fields: [city_id], references: [id], onDelete: Cascade)

  @@id([zone_id, city_id])
  @@map("zone_to_city")
}

// Base shipping costs for carriers between zones
model cost {
  id                String    @id @default(uuid()) @db.Uuid
  from_country_code String    @db.Char(2)
  from_zone         String?
  to_country_code   String    @db.Char(2)
  to_zone           String?
  carrier_code      String
  from_weight       Decimal   @db.Decimal(12, 3)
  to_weight         Decimal   @db.Decimal(12, 3)
  rate_type         String
  cost_amount       Decimal   @db.Decimal(12, 2)
  base_fee          Decimal?  @db.Decimal(12, 2)
  currency          String    @default("SAR")
  valid_from        DateTime? @db.Date
  valid_to          DateTime? @db.Date
  created_at        DateTime? @default(now()) @db.Timestamptz(6)
  updated_at        DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  carrier            carrier @relation(fields: [carrier_code], references: [carrier_code], onDelete: NoAction, onUpdate: NoAction)
  from_country       country @relation("cost_from_country", fields: [from_country_code], references: [code], onDelete: NoAction, onUpdate: NoAction)
  to_country         country @relation("cost_to_country", fields: [to_country_code], references: [code], onDelete: NoAction, onUpdate: NoAction)
  from_zone_relation zone?   @relation("cost_from_zone", fields: [from_country_code, from_zone, carrier_code], references: [country_code, zone_code, carrier_code], onDelete: NoAction, onUpdate: NoAction, map: "costs_from_zone_fkey")
  to_zone_relation   zone?   @relation("cost_to_zone", fields: [to_country_code, to_zone, carrier_code], references: [country_code, zone_code, carrier_code], onDelete: NoAction, onUpdate: NoAction, map: "costs_to_zone_fkey")

  @@index([from_country_code, to_country_code, carrier_code, from_weight, to_weight], map: "idx_costs_lookup")
  @@map("cost")
}

// Customer-facing shipping rates based on plans and costs
model rate {
  id                String    @id @default(uuid()) @db.Uuid
  from_country_code String    @db.Char(2)
  from_zone         String?
  to_country_code   String    @db.Char(2)
  to_zone           String?
  carrier_code      String
  from_weight       Decimal   @db.Decimal(12, 3)
  to_weight         Decimal   @db.Decimal(12, 3)
  rate_type         String
  rate_amount       Decimal   @db.Decimal(12, 2)
  base_fee          Decimal?  @db.Decimal(12, 2)
  currency          String    @default("SAR")
  plan_id           String    @db.Uuid
  delivery_days     Int?
  valid_from        DateTime? @db.Date
  valid_to          DateTime? @db.Date
  created_at        DateTime? @default(now()) @db.Timestamptz(6)
  updated_at        DateTime? @updatedAt @db.Timestamptz(6)
  user_id           String?   @db.Uuid
  organization_id   String?   @db.Uuid

  // --- Relationships ---
  carrier            carrier       @relation(fields: [carrier_code], references: [carrier_code], onDelete: NoAction, onUpdate: NoAction)
  from_country       country       @relation("rate_from_country", fields: [from_country_code], references: [code], onDelete: NoAction, onUpdate: NoAction)
  organization       organization? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  plan               plan          @relation(fields: [plan_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  to_country         country       @relation("rate_to_country", fields: [to_country_code], references: [code], onDelete: NoAction, onUpdate: NoAction)
  user               user?         @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  from_zone_relation zone?         @relation("rate_from_zone", fields: [from_country_code, from_zone, carrier_code], references: [country_code, zone_code, carrier_code], onDelete: NoAction, onUpdate: NoAction, map: "rates_from_zone_fkey")
  to_zone_relation   zone?         @relation("rate_to_zone", fields: [to_country_code, to_zone, carrier_code], references: [country_code, zone_code, carrier_code], onDelete: NoAction, onUpdate: NoAction, map: "rates_to_zone_fkey")

  @@index([from_country_code, to_country_code, carrier_code, from_weight, to_weight], map: "idx_rates_lookup")
  @@index([organization_id], map: "idx_rates_organization_id")
  @@index([plan_id], map: "idx_rates_plan_id")
  @@index([user_id], map: "idx_rates_user_id")
  @@map("rate")
}

// ========================================================================
// --- SUBSCRIPTION & BILLING MODELS ---
// ========================================================================

// Subscription plans with different features and pricing
model plan {
  id                 String   @id @default(uuid()) @db.Uuid
  name               String
  code               String   @unique
  description        String?
  price              Decimal  @db.Decimal(12, 2)
  duration_in_months Int?     @default(1)
  is_active          Boolean? @default(true)
  upgrade_to         String[] @db.Uuid
  trial_period_days  Int?
  created_at         DateTime @default(now()) @db.Timestamptz(6)
  updated_at         DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  coupons       coupon[]
  organizations organization[]
  plan_services plan_service[]
  rates         rate[]

  @@map("plan")
}

// Available services that can be included in plans
model service {
  id          String   @id @default(uuid()) @db.Uuid
  code        String   @unique
  name        String
  description String?
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  plan_services          plan_service[]
  user_service_overrides user_service_override[]

  @@map("service")
}

// Mapping between plans and their included services
model plan_service {
  id         String   @id @default(uuid()) @db.Uuid
  plan_id    String?  @db.Uuid
  service_id String?  @db.Uuid
  enabled    Boolean? @default(true)
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  plan    plan?    @relation(fields: [plan_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  service service? @relation(fields: [service_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("plan_service")
}

// Service overrides for specific users
model user_service_override {
  user_id    String    @db.Uuid
  service_id String    @db.Uuid
  is_enabled Boolean
  reason     String?
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  service service @relation(fields: [service_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    user    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([user_id, service_id])
  @@map("user_service_override")
}

// Discount coupons for plans
model coupon {
  id                 String    @id @default(uuid()) @db.Uuid
  code               String    @unique
  discount_type      String
  discount_value     Decimal   @db.Decimal(12, 2)
  usage_limit        Int?
  used_count         Int?      @default(0)
  max_usage_per_user Int?
  min_order_value    Decimal?  @db.Decimal(12, 2)
  plan_id            String?   @db.Uuid
  expires_at         DateTime? @db.Timestamptz(6)
  is_active          Boolean?  @default(true)
  created_at         DateTime? @default(now()) @db.Timestamptz(6)
  updated_at         DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  coupon_usages coupon_usage[]
  plan          plan?          @relation(fields: [plan_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([code], map: "idx_coupons_code")
  @@index([plan_id], map: "idx_coupons_plan_id")
  @@map("coupon")
}

// Tracking coupon usage by users
model coupon_usage {
  id         String    @id @default(uuid()) @db.Uuid
  coupon_id  String?   @db.Uuid
  user_id    String    @db.Uuid
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  coupon coupon? @relation(fields: [coupon_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user   user    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([coupon_id], map: "idx_coupon_usages_coupon_id")
  @@index([user_id], map: "idx_coupon_usages_user_id")
  @@map("coupon_usage")
}

// ========================================================================
// --- WALLET & TRANSACTION MODELS ---
// ========================================================================

// User wallets for account balance management
model wallet {
  id         String    @id @default(uuid()) @db.Uuid
  user_id    String    @unique @db.Uuid
  name       String
  balance    Decimal   @default(0.00) @db.Decimal(12, 2)
  currency   String?   @default("SAR")
  is_active  Boolean?  @default(true)
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  wallet_transactions wallet_transaction[]
  user                user                 @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([user_id], map: "idx_wallets_user_id")
  @@map("wallet")
}

// Financial transactions in the system
model transaction {
  id                     String    @id @default(uuid()) @db.Uuid
  user_id                String    @db.Uuid
  amount                 Decimal   @db.Decimal(12, 2)
  currency               String?   @default("SAR")
  status                 String
  debit_type             String?
  debit_id               String?   @db.Uuid
  credit_type            String?
  credit_id              String?   @db.Uuid
  payment_gateway        String?
  gateway_transaction_id String?
  gateway_response_code  String?
  description            String?
  created_at             DateTime? @default(now()) @db.Timestamptz(6)
  updated_at             DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user                user                 @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  wallet_transactions wallet_transaction[]

  @@index([created_at], map: "idx_transactions_created_at")
  @@index([gateway_transaction_id], map: "idx_transactions_gateway_transaction_id")
  @@index([status], map: "idx_transactions_status")
  @@index([user_id], map: "idx_transactions_user_id")
  @@map("transaction")
}

// Link between wallet and transaction with balance tracking
model wallet_transaction {
  id             String    @id @default(uuid()) @db.Uuid
  wallet_id      String    @db.Uuid
  transaction_id String    @db.Uuid
  type           String
  amount         Decimal   @db.Decimal(12, 2)
  balance_before Decimal?  @db.Decimal(12, 2)
  balance_after  Decimal?  @db.Decimal(12, 2)
  created_at     DateTime? @default(now()) @db.Timestamptz(6)
  updated_at     DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  transaction transaction @relation(fields: [transaction_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  wallet      wallet      @relation(fields: [wallet_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([transaction_id], map: "idx_wallet_transactions_transaction_id")
  @@index([wallet_id], map: "idx_wallet_transactions_wallet_id")
  @@map("wallet_transaction")
}

// ========================================================================
// --- PRODUCT & INVENTORY MODELS ---
// ========================================================================

// Products that can be sold and shipped
model product {
  id                String    @id @default(uuid()) @db.Uuid
  user_id           String    @db.Uuid
  external_id       String?
  platform_id       String?   @db.Uuid
  name              String
  sku               String?
  barcode           String?
  description       String?
  price             Decimal?  @db.Decimal(12, 2)
  currency          String?   @default("SAR")
  hs_code           String?
  country_of_origin String?   @default("CN") @db.Char(2)
  weight_grams      Decimal?  @db.Decimal(12, 3)
  length_cm         Decimal?  @db.Decimal(12, 2)
  width_cm          Decimal?  @db.Decimal(12, 2)
  height_cm         Decimal?  @db.Decimal(12, 2)
  is_active         Boolean?  @default(true)
  synced_at         DateTime? @db.Timestamptz(6)
  created_at        DateTime? @default(now()) @db.Timestamptz(6)
  updated_at        DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order_items       order_item[]
  product_images    product_image[]
  product_inventory product_inventory[]
  product_sync_logs product_sync_log[]
  product_variants  product_variant[]
  platform          platform?           @relation(fields: [platform_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user              user?               @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([user_id, sku])
  @@index([name])
  @@index([barcode], map: "idx_products_barcode")
  @@index([platform_id], map: "idx_products_platform_id")
  @@index([sku], map: "idx_products_sku")
  @@index([user_id], map: "idx_products_user_id")
  @@map("product")
}

// Product variations (size, color, etc.)
model product_variant {
  id           String    @id @default(uuid()) @db.Uuid
  product_id   String?   @db.Uuid
  user_id      String?   @db.Uuid
  name         String
  sku          String?
  barcode      String?
  price        Decimal?  @db.Decimal(12, 2)
  weight_grams Decimal?  @db.Decimal(12, 3)
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  product_inventory product_inventory[]
  product           product?            @relation(fields: [product_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user              user?               @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([barcode], map: "idx_product_variants_barcode")
  @@index([product_id], map: "idx_product_variants_product_id")
  @@index([sku], map: "idx_product_variants_sku")
  @@map("product_variant")
}

// Product images for display
model product_image {
  id         String    @id @default(uuid()) @db.Uuid
  product_id String?   @db.Uuid
  user_id    String?   @db.Uuid
  image_url  String
  is_primary Boolean?  @default(false)
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  product product? @relation(fields: [product_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    user?    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("product_image")
}

// ========================================================================
// --- WAREHOUSE & INVENTORY MODELS ---
// ========================================================================

// Warehouse locations for inventory storage
model warehouse {
  id         String    @id @default(uuid()) @db.Uuid
  user_id    String?   @db.Uuid
  name       String
  code       String    @unique
  address_1  String?
  address_2  String?
  city_name  String?
  state      String?
  zip        String?
  country    String?
  city_id    String?   @db.Uuid
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  is_active  Boolean?

  // --- Relationships ---
  warehouse_bins    warehouse_bin[]
  product_inventory product_inventory[]
  user              user?               @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  city              city?               @relation(fields: [city_id], references: [id])

  @@index([code], map: "idx_warehouses_code")
  @@index([user_id], map: "idx_warehouses_user_id")
  @@map("warehouse")
}

// Storage bins within warehouses
model warehouse_bin {
  id           String    @id @default(uuid()) @db.Uuid
  warehouse_id String?   @db.Uuid
  user_id      String?   @db.Uuid
  code         String
  zone         String?
  description  String?
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user              user?               @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  warehouse         warehouse?          @relation(fields: [warehouse_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product_inventory product_inventory[]

  @@index([code], map: "idx_warehouse_bins_code")
  @@index([warehouse_id], map: "idx_warehouse_bins_warehouse_id")
  @@map("warehouse_bin")
}

// Inventory tracking for products in warehouses
model product_inventory {
  id              String    @id @default(uuid()) @db.Uuid
  product_id      String?   @db.Uuid
  variant_id      String?   @db.Uuid
  warehouse_id    String?   @db.Uuid
  bin_id          String?   @db.Uuid
  user_id         String?   @db.Uuid
  batch_number    String?
  expiration_date DateTime? @db.Date
  quantity        Int
  created_at      DateTime? @default(now()) @db.Timestamptz(6)
  updated_at      DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  bin             warehouse_bin?   @relation(fields: [bin_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product         product?         @relation(fields: [product_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user            user?            @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product_variant product_variant? @relation(fields: [variant_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  warehouse       warehouse?       @relation(fields: [warehouse_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([product_id], map: "idx_product_inventory_product_id")
  @@index([variant_id], map: "idx_product_inventory_variant_id")
  @@index([warehouse_id], map: "idx_product_inventory_warehouse_id")
  @@map("product_inventory")
}

// ========================================================================
// --- ORDER & SHIPMENT MODELS ---
// ========================================================================

// Main order entity containing shipment details
model order {
  id                String         @id @default(uuid()) @db.Uuid
  order_number      String         @unique
  user_id           String         @db.Uuid
  type              OrderType
  from_address      Json?
  to_address        Json?
  platform_order_id String?        @db.Uuid
  status            OrderStatus    @default(PENDING)
  description       String?
  payment_status    PaymentStatus? @default(UNPAID)
  tracking_number   String?
  declared_value    Decimal?       @db.Decimal(12, 2)
  cod_amount        Decimal?       @db.Decimal(12, 2)
  source            String?
  carrier_code      String?
  metadata          Json?
  // E-invoice specific fields
  recipient_phone   String?
  link_token        String?        @unique
  link_expires_at   DateTime?      @db.Timestamptz(6)
  created_at        DateTime?      @default(now()) @db.Timestamptz(6)
  updated_at        DateTime?      @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order_charges  order_charge[]
  order_items    order_item[]
  carrier        carrier?        @relation(fields: [carrier_code], references: [carrier_code], onDelete: NoAction, onUpdate: NoAction)
  platform_order platform_order? @relation(fields: [platform_order_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user           user            @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  parcels        parcel[]
  tracking_data  tracking_data[]

  @@index([carrier_code], map: "idx_orders_carrier_code")
  @@index([created_at], map: "idx_orders_created_at")
  @@index([platform_order_id], map: "idx_orders_platform_order_id")
  @@index([status], map: "idx_orders_status")
  @@index([tracking_number], map: "idx_orders_tracking_number")
  @@index([user_id], map: "idx_orders_user_id")
  @@index([link_token], map: "idx_orders_link_token")
  @@map("order")
}

// Items within an order
model order_item {
  id           String    @id @default(uuid()) @db.Uuid
  user_id      String    @db.Uuid
  order_id     String    @db.Uuid
  product_id   String?   @db.Uuid
  product_name String
  product_sku  String?
  quantity     Int       @default(1)
  unit_price   Decimal   @db.Decimal(12, 2)
  total_price  Decimal   @db.Decimal(12, 2)
  metadata     Json?
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order   order    @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product product? @relation(fields: [product_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    user     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([order_id], map: "idx_order_items_order_id")
  @@index([product_id], map: "idx_order_items_product_id")
  @@index([user_id], map: "idx_order_items_user_id")
  @@map("order_item")
}

// Additional charges on orders (shipping, handling, etc.)
model order_charge {
  id         String    @id @default(uuid()) @db.Uuid
  user_id    String    @db.Uuid
  order_id   String    @db.Uuid
  name       String
  type       String?
  amount     Decimal   @db.Decimal(12, 2)
  currency   String?   @default("SAR")
  metadata   Json?
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order order @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user  user  @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("order_charge")
}

// Physical packages/parcels in a shipment
model parcel {
  id                 String    @id @default(uuid()) @db.Uuid
  user_id            String    @db.Uuid
  order_id           String    @db.Uuid
  length             Decimal   @db.Decimal(12, 2)
  width              Decimal   @db.Decimal(12, 2)
  height             Decimal   @db.Decimal(12, 2)
  actual_weight      Decimal?  @db.Decimal(12, 3)
  dimensional_weight Decimal?  @db.Decimal(12, 3)
  final_weight       Decimal?  @db.Decimal(12, 3)
  tracking_number    String?
  metadata           Json?
  created_at         DateTime? @default(now()) @db.Timestamptz(6)
  updated_at         DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order order @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user  user  @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([order_id], map: "idx_parcels_order_id")
  @@index([tracking_number], map: "idx_parcels_tracking_number")
  @@map("parcel")
}

// Tracking updates for shipments
model tracking_data {
  id         String    @id @default(uuid()) @db.Uuid
  order_id   String?   @db.Uuid
  status     String?
  checkpoint String?
  created_at DateTime? @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order order? @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([order_id], map: "idx_tracking_data_order_id")
  @@map("tracking_data")
}

// ========================================================================
// --- E-COMMERCE PLATFORM INTEGRATION MODELS ---
// ========================================================================

// E-commerce platforms (Shopify, WooCommerce, etc.)
model platform {
  id         String    @id @default(uuid()) @db.Uuid
  code       String    @unique
  name       String
  logo_url   String?
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  platform_integrations platform_integration[]
  platform_orders       platform_order[]
  product_sync_logs     product_sync_log[]
  products              product[]

  @@map("platform")
}

// User connections to e-commerce platforms
model platform_integration {
  id               String    @id @default(uuid()) @db.Uuid
  user_id          String?   @db.Uuid
  platform_id      String?   @db.Uuid
  external_shop_id String?
  access_token     String?
  refresh_token    String?
  token_expires_at DateTime? @db.Timestamptz(6)
  shop_name        String?
  connected_at     DateTime? @default(now()) @db.Timestamptz(6)
  is_active        Boolean?  @default(true)
  updated_at       DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  platform platform? @relation(fields: [platform_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user     user?     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([platform_id], map: "idx_platform_integrations_platform_id")
  @@index([user_id], map: "idx_platform_integrations_user_id")
  @@map("platform_integration")
}

// Orders imported from e-commerce platforms
model platform_order {
  id                  String    @id @default(uuid()) @db.Uuid
  user_id             String    @db.Uuid
  platform_id         String    @db.Uuid
  external_order_id   String
  status              String?
  customer_name       String?
  customer_address_id String?   @db.Uuid
  items               Json
  raw_payload         Json?
  synced_at           DateTime? @default(now()) @db.Timestamptz(6)
  created_at          DateTime? @default(now()) @db.Timestamptz(6)
  updated_at          DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  orders   order[]
  address  address? @relation(fields: [customer_address_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  platform platform @relation(fields: [platform_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user     user     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([external_order_id], map: "idx_platform_orders_external_order_id")
  @@index([platform_id], map: "idx_platform_orders_platform_id")
  @@index([user_id], map: "idx_platform_orders_user_id")
  @@map("platform_order")
}

// Product synchronization logs from platforms
model product_sync_log {
  id            String    @id @default(uuid()) @db.Uuid
  product_id    String?   @db.Uuid
  platform_id   String?   @db.Uuid
  user_id       String?   @db.Uuid
  created_at    DateTime? @default(now()) @db.Timestamptz(6)
  updated_at    DateTime  @updatedAt @db.Timestamptz(6)
  sync_status   String?
  error_message String?

  // --- Relationships ---
  platform platform? @relation(fields: [platform_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product  product?  @relation(fields: [product_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user     user?     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("product_sync_log")
}

// ========================================================================
// --- SYSTEM & AUDIT MODELS ---
// ========================================================================

// Activity logs for user actions
model activity_log {
  id         String    @id @default(uuid()) @db.Uuid
  user_id    String?   @db.Uuid
  table_name String?
  record_id  String?   @db.Uuid
  action     String?
  message    String?
  level      String?
  data       Json?
  created_at DateTime? @default(now()) @db.Timestamptz(6)

  // --- Relationships ---
  user user? @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([created_at], map: "idx_activity_logs_created_at")
  @@index([user_id], map: "idx_activity_logs_user_id")
  @@map("activity_log")
}

// Audit trail for data changes
model audit_trail {
  id            String    @id @default(uuid()) @db.Uuid
  user_id       String?   @db.Uuid
  table_name    String?
  record_id     String?
  field_changed String?
  old_value     String?
  new_value     String?
  created_at    DateTime? @default(now()) @db.Timestamptz(6)

  // --- Relationships ---
  user user? @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([created_at], map: "idx_audit_trail_created_at")
  @@index([user_id], map: "idx_audit_trail_user_id")
  @@map("audit_trail")
}

// Webhook logs for external integrations
model webhook_log {
  id         String    @id @default(uuid()) @db.Uuid
  payload    Json?
  source     String?
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)

  @@index([created_at], map: "idx_webhook_logs_received_at")
  @@map("webhook_log")
}

// Content translations for multi-language support
model content_translation {
  id               String   @id @default(uuid()) @db.Uuid
  table_name       String
  record_id        String   @db.Uuid
  field_name       String
  language_code    String
  translated_value String
  created_at       DateTime @default(now()) @db.Timestamptz(6)
  updated_at       DateTime @updatedAt @db.Timestamptz(6)

  @@index([table_name, record_id], map: "idx_content_translations_table_record")
  @@map("content_translation")
}
