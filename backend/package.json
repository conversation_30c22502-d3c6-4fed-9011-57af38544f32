{"name": "shiptag-backend", "version": "1.0.0", "description": "ShipTag Backend API with OTP Authentication", "main": "dist/server.js", "scripts": {"dev": "tsx src/server.ts", "build": "tsc", "start": "node dist/server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "dependencies": {"@auth/express": "^0.11.0", "@prisma/client": "^6.11.1", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "resend": "^4.6.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "devDependencies": {"@types/node": "^22.16.3"}}